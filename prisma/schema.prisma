// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model - Regular customers only
model User {
  id          String    @id @default(cuid())
  email       String    @unique
  name        String
  password    String
  avatar      String?
  phone       String?
  dateOfBirth DateTime?
  gender      Gender?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  orders             Order[]
  addresses          Address[]
  reviews            Review[]
  cart               Cart?
  passwordResetToken PasswordResetToken?

  @@map("users")
}

// Password reset token model
model PasswordResetToken {
  id        String   @id @default(cuid())
  userId    String   @unique
  token     String   @unique
  expiresAt DateTime
  used      Boolean  @default(false)
  createdAt DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("password_reset_tokens")
}

// AdminUser model - Admin and moderator accounts
model AdminUser {
  id          String    @id @default(cuid())
  email       String    @unique
  name        String
  password    String
  role        AdminRole @default(MODERATOR)
  avatar      String?
  phone       String?
  isActive    Boolean   @default(true)
  permissions Json? // Fine-grained permissions for moderators
  lastLoginAt DateTime?
  department  String? // Organization department
  createdBy   String? // Which admin created this account
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  createdByAdmin        AdminUser?     @relation("AdminHierarchy", fields: [createdBy], references: [id])
  createdAdmins         AdminUser[]    @relation("AdminHierarchy")
  posts                 Post[]
  pages                 Page[]
  createdNotifications  Notification[] @relation("NotificationCreator")
  targetedNotifications Notification[] @relation("NotificationTarget")
  auditLogs             AuditLog[]     @relation("AuditLogAdmin")

  @@map("admin_users")
}

// Category model
model Category {
  id          String   @id @default(cuid())
  name        String
  description String?
  slug        String   @unique
  image       String?
  parentId    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")
  products Product[]
  posts    Post[]

  @@map("categories")
}

// Brand model
model Brand {
  id              String     @id @default(cuid())
  name            String
  description     String?
  slug            String     @unique
  logo            String?
  logoType        MediaType? @default(INTERNAL)
  externalLogoUrl String?
  website         String?
  isActive        Boolean    @default(true)
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // Relations
  products Product[]

  @@map("brands")
}

// Attribute model
model Attribute {
  id           String        @id @default(cuid())
  name         String
  slug         String        @unique
  description  String?
  type         AttributeType @default(TEXT)
  isRequired   Boolean       @default(false)
  isFilterable Boolean       @default(true)
  sortOrder    Int           @default(0)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt

  // Relations
  values   AttributeValue[]
  products ProductAttribute[]
  presets  PresetAttribute[]

  @@map("attributes")
}

// Attribute Value model
model AttributeValue {
  id          String   @id @default(cuid())
  attributeId String
  value       String
  slug        String
  sortOrder   Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  attribute      Attribute          @relation(fields: [attributeId], references: [id], onDelete: Cascade)
  products       ProductAttribute[]
  presetDefaults PresetAttribute[]

  @@unique([attributeId, slug])
  @@map("attribute_values")
}

// Product Attribute (Many-to-Many relationship)
model ProductAttribute {
  id               String   @id @default(cuid())
  productId        String
  attributeId      String
  attributeValueId String
  createdAt        DateTime @default(now())

  // Relations
  product        Product        @relation(fields: [productId], references: [id], onDelete: Cascade)
  attribute      Attribute      @relation(fields: [attributeId], references: [id], onDelete: Cascade)
  attributeValue AttributeValue @relation(fields: [attributeValueId], references: [id], onDelete: Cascade)

  @@unique([productId, attributeId])
  @@map("product_attributes")
}

enum AttributeType {
  TEXT
  NUMBER
  COLOR
  SIZE
  BOOLEAN
  SELECT
  MULTI_SELECT
}

// Attribute Preset model
model AttributePreset {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  isActive    Boolean  @default(true)
  sortOrder   Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  attributes PresetAttribute[]
  products   Product[]

  @@map("attribute_presets")
}

// Preset Attribute (Many-to-Many relationship between presets and attributes)
model PresetAttribute {
  id             String   @id @default(cuid())
  presetId       String
  attributeId    String
  isRequired     Boolean  @default(false)
  defaultValueId String?
  sortOrder      Int      @default(0)
  createdAt      DateTime @default(now())

  // Relations
  preset       AttributePreset @relation(fields: [presetId], references: [id], onDelete: Cascade)
  attribute    Attribute       @relation(fields: [attributeId], references: [id], onDelete: Cascade)
  defaultValue AttributeValue? @relation(fields: [defaultValueId], references: [id])

  @@unique([presetId, attributeId])
  @@map("preset_attributes")
}

// Product model
model Product {
  id             String        @id @default(cuid())
  name           String
  description    String
  price          Float
  salePrice      Float?
  images         String[]
  imageTypes     MediaType[]   @default([INTERNAL])
  externalImages String[]
  categoryId     String
  brandId        String?
  presetId       String?
  stock          Int           @default(0)
  sku            String        @unique
  slug           String        @unique
  featured       Boolean       @default(false)
  status         ProductStatus @default(ACTIVE)
  tags           String[]
  avgRating      Float         @default(0)
  reviewCount    Int           @default(0)
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt

  // Relations
  category         Category           @relation(fields: [categoryId], references: [id])
  brand            Brand?             @relation(fields: [brandId], references: [id])
  preset           AttributePreset?   @relation(fields: [presetId], references: [id])
  cartItems        CartItem[]
  orderItems       OrderItem[]
  reviews          Review[]
  inventoryEntries InventoryEntry[]
  ProductAttribute ProductAttribute[]

  @@map("products")
}

// Cart model
model Cart {
  id        String   @id @default(cuid())
  userId    String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user  User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  items CartItem[]

  @@map("carts")
}

// Cart Item model
model CartItem {
  id        String   @id @default(cuid())
  cartId    String
  productId String
  quantity  Int
  price     Float
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  cart    Cart    @relation(fields: [cartId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([cartId, productId])
  @@map("cart_items")
}

// Order model
model Order {
  id              String        @id @default(cuid())
  userId          String
  total           Float
  status          OrderStatus   @default(PENDING)
  paymentMethod   PaymentMethod @default(COD)
  paymentStatus   PaymentStatus @default(PENDING)
  shippingAddress Json
  billingAddress  Json?
  notes           String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  user  User        @relation(fields: [userId], references: [id])
  items OrderItem[]

  @@map("orders")
}

// Order Item model
model OrderItem {
  id        String   @id @default(cuid())
  orderId   String
  productId String
  quantity  Int
  price     Float
  total     Float
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

// Address model
model Address {
  id        String   @id @default(cuid())
  userId    String
  fullName  String
  phone     String
  address   String
  ward      String
  district  String
  province  String
  isDefault Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("addresses")
}

// Review model
model Review {
  id        String   @id @default(cuid())
  userId    String
  productId String
  rating    Int      @default(5)
  comment   String?
  images    String[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId])
  @@map("reviews")
}

model Post {
  id                    String     @id @default(cuid())
  title                 String
  content               String
  excerpt               String?
  slug                  String     @unique
  status                PostStatus @default(DRAFT)
  featured              Boolean    @default(false)
  featuredImage         String?
  featuredImageType     MediaType? @default(INTERNAL)
  externalFeaturedImage String?
  tags                  String[]
  categoryId            String?
  authorId              String
  viewCount             Int        @default(0)
  createdAt             DateTime   @default(now())
  updatedAt             DateTime   @updatedAt

  // Relations
  category Category? @relation(fields: [categoryId], references: [id])
  author   AdminUser @relation(fields: [authorId], references: [id])

  @@map("posts")
}

model Page {
  id              String     @id @default(cuid())
  title           String
  content         String
  excerpt         String?
  slug            String     @unique
  status          PageStatus @default(DRAFT)
  featured        Boolean    @default(false)
  featuredImage   String?
  metaTitle       String?
  metaDescription String?
  authorId        String
  viewCount       Int        @default(0)
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // Relations
  author AdminUser @relation(fields: [authorId], references: [id])

  @@map("pages")
}

model Setting {
  id    String @id @default(cuid())
  key   String @unique
  value Json
  type  String @default("string") // string, number, boolean, json

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Inventory Entry model - Tracks stock for each product
model InventoryEntry {
  id        String   @id @default(cuid())
  productId String
  quantity  Int      @default(0)
  reserved  Int      @default(0) // Reserved for pending orders
  available Int      @default(0) // Available = quantity - reserved
  minStock  Int      @default(0) // Minimum stock level for alerts
  maxStock  Int? // Maximum stock level
  location  String? // Warehouse location
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  product        Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  stockMovements StockMovement[]

  @@unique([productId])
  @@map("inventory_entries")
}

// Stock Movement model - Tracks all stock changes
model StockMovement {
  id               String            @id @default(cuid())
  inventoryEntryId String
  type             StockMovementType
  quantity         Int // Positive for IN, negative for OUT
  reason           String? // Reason for the movement
  reference        String? // Reference to order, return, etc.
  notes            String?
  createdBy        String? // Admin who made the change
  createdAt        DateTime          @default(now())

  // Relations
  inventoryEntry InventoryEntry @relation(fields: [inventoryEntryId], references: [id], onDelete: Cascade)

  @@map("stock_movements")
}

// Enums
enum AdminRole {
  ADMIN
  MODERATOR
}

enum Gender {
  MALE
  FEMALE
  OTHER
}

enum MediaType {
  INTERNAL
  EXTERNAL
}

enum ProductStatus {
  ACTIVE
  INACTIVE
  OUT_OF_STOCK
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
}

enum PaymentMethod {
  COD
  BANK_TRANSFER
  CREDIT_CARD
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}

enum PostStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum PageStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum StockMovementType {
  IN // Stock increase (purchase, return, adjustment)
  OUT // Stock decrease (sale, damage, adjustment)
  TRANSFER // Stock transfer between locations
  ADJUSTMENT // Manual adjustment
}

// Notification model - Admin notifications system
model Notification {
  id         String               @id @default(cuid())
  title      String
  message    String
  type       NotificationType     @default(INFO)
  priority   NotificationPriority @default(NORMAL)
  targetType NotificationTarget   @default(ALL_ADMINS)
  targetId   String? // Specific admin user ID if targeted
  isRead     Boolean              @default(false)
  readAt     DateTime?
  actionUrl  String? // URL to navigate when clicked
  metadata   Json? // Additional data for the notification
  expiresAt  DateTime? // Optional expiration date
  createdBy  String? // Admin who created the notification
  createdAt  DateTime             @default(now())
  updatedAt  DateTime             @updatedAt

  // Relations
  creator AdminUser? @relation("NotificationCreator", fields: [createdBy], references: [id])
  target  AdminUser? @relation("NotificationTarget", fields: [targetId], references: [id])

  @@map("notifications")
}

// Audit Log model - Track admin actions
model AuditLog {
  id          String   @id @default(cuid())
  action      String // Action performed (CREATE, UPDATE, DELETE, etc.)
  resource    String // Resource type (Product, User, Order, etc.)
  resourceId  String? // ID of the affected resource
  oldValues   Json? // Previous values (for updates)
  newValues   Json? // New values (for creates/updates)
  description String? // Human-readable description
  ipAddress   String? // IP address of the admin
  userAgent   String? // User agent string
  adminId     String // Admin who performed the action
  createdAt   DateTime @default(now())

  // Relations
  admin AdminUser @relation("AuditLogAdmin", fields: [adminId], references: [id])

  @@map("audit_logs")
}

// Notification enums
enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
  SYSTEM
}

enum NotificationPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum NotificationTarget {
  ALL_ADMINS
  SPECIFIC_ADMIN
  ROLE_ADMIN
  ROLE_MODERATOR
}
