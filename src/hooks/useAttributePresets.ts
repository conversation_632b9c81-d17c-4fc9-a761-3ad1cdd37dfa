import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";
import {
  AttributePreset,
  AttributePresetFormData,
  AttributePresetFilters,
  AttributePresetStats,
  AttributePresetListResponse,
  AttributePresetResponse,
  AttributePresetStatsResponse,
} from "@/types/attribute-preset";

interface UseAttributePresetsReturn {
  // State
  presets: AttributePreset[];
  loading: boolean;
  error: string | null;
  filters: AttributePresetFilters;
  stats: AttributePresetStats | null;

  // Actions
  fetchPresets: () => Promise<void>;
  createPreset: (
    data: AttributePresetFormData
  ) => Promise<AttributePreset | null>;
  updatePreset: (
    id: string,
    data: Partial<AttributePresetFormData>
  ) => Promise<AttributePreset | null>;
  deletePreset: (id: string) => Promise<boolean>;
  bulkDelete: (ids: string[]) => Promise<boolean>;
  bulkUpdate: (
    ids: string[],
    data: Partial<AttributePresetFormData>
  ) => Promise<boolean>;
  setFilters: (filters: Partial<AttributePresetFilters>) => void;
  resetFilters: () => void;
  fetchStats: () => Promise<void>;
  refresh: () => Promise<void>;
}

const defaultFilters: AttributePresetFilters = {
  page: 1,
  limit: 20,
  sortBy: "sortOrder",
  sortOrder: "asc",
};

export function useAttributePresets(): UseAttributePresetsReturn {
  const [presets, setPresets] = useState<AttributePreset[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFiltersState] =
    useState<AttributePresetFilters>(defaultFilters);
  const [stats, setStats] = useState<AttributePresetStats | null>(null);

  const fetchPresets = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      params.append("page", filters.page.toString());
      params.append("limit", filters.limit.toString());
      params.append("sortBy", filters.sortBy);
      params.append("sortOrder", filters.sortOrder);

      if (filters.search) {
        params.append("search", filters.search);
      }

      if (filters.isActive !== undefined) {
        params.append("isActive", filters.isActive.toString());
      }

      const response = await fetch(`/api/admin/attribute-presets?${params}`);
      const result: AttributePresetListResponse = await response.json();

      if (result.success && result.data) {
        setPresets(result.data);
      } else {
        throw new Error(result.error || "Không thể tải danh sách preset");
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Có lỗi xảy ra";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  const createPreset = async (
    data: AttributePresetFormData
  ): Promise<AttributePreset | null> => {
    try {
      const response = await fetch("/api/admin/attribute-presets", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result: AttributePresetResponse = await response.json();

      if (result.success && result.data) {
        toast.success(result.message || "Preset đã được tạo thành công");
        await fetchPresets(); // Refresh list
        return result.data;
      } else {
        throw new Error(result.error || "Không thể tạo preset");
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Có lỗi xảy ra";
      toast.error(errorMessage);
      return null;
    }
  };

  const updatePreset = async (
    id: string,
    data: Partial<AttributePresetFormData>
  ): Promise<AttributePreset | null> => {
    try {
      const response = await fetch(`/api/admin/attribute-presets/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result: AttributePresetResponse = await response.json();

      if (result.success && result.data) {
        toast.success(result.message || "Preset đã được cập nhật thành công");
        await fetchPresets(); // Refresh list
        return result.data;
      } else {
        throw new Error(result.error || "Không thể cập nhật preset");
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Có lỗi xảy ra";
      toast.error(errorMessage);
      return null;
    }
  };

  const deletePreset = async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/admin/attribute-presets/${id}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (result.success) {
        toast.success(result.message || "Preset đã được xóa thành công");
        await fetchPresets(); // Refresh list
        return true;
      } else {
        throw new Error(result.error || "Không thể xóa preset");
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Có lỗi xảy ra";
      toast.error(errorMessage);
      return false;
    }
  };

  const bulkDelete = async (ids: string[]): Promise<boolean> => {
    try {
      let successCount = 0;
      for (const id of ids) {
        const success = await deletePreset(id);
        if (success) successCount++;
      }

      if (successCount === ids.length) {
        toast.success(`Đã xóa thành công ${successCount} preset`);
        return true;
      } else if (successCount > 0) {
        toast.warning(`Đã xóa thành công ${successCount}/${ids.length} preset`);
        return false;
      } else {
        toast.error("Không thể xóa preset nào");
        return false;
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi xóa preset");
      return false;
    }
  };

  const bulkUpdate = async (
    ids: string[],
    data: Partial<AttributePresetFormData>
  ): Promise<boolean> => {
    try {
      const results = await Promise.all(
        ids.map((id) => updatePreset(id, data))
      );

      return results.every((result) => result !== null);
    } catch (error) {
      toast.error("Có lỗi xảy ra khi cập nhật preset");
      return false;
    }
  };

  const setFilters = (newFilters: Partial<AttributePresetFilters>) => {
    setFiltersState((prev) => ({ ...prev, ...newFilters }));
  };

  const resetFilters = () => {
    setFiltersState(defaultFilters);
  };

  const fetchStats = async () => {
    try {
      const response = await fetch("/api/admin/attribute-presets/stats");
      const result: AttributePresetStatsResponse = await response.json();

      if (result.success && result.data) {
        setStats(result.data);
      } else {
        throw new Error(result.error || "Không thể tải thống kê");
      }
    } catch (error) {
      console.error("Fetch stats error:", error);
    }
  };

  const refresh = async () => {
    await Promise.all([fetchPresets(), fetchStats()]);
  };

  // Fetch presets when filters change
  useEffect(() => {
    fetchPresets();
  }, [fetchPresets]);

  // Fetch stats on mount
  useEffect(() => {
    fetchStats();
  }, []);

  return {
    presets,
    loading,
    error,
    filters,
    stats,
    fetchPresets,
    createPreset,
    updatePreset,
    deletePreset,
    bulkDelete,
    bulkUpdate,
    setFilters,
    resetFilters,
    fetchStats,
    refresh,
  };
}
