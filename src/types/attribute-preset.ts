import { Attribute, AttributeValue } from './attribute';

export interface AttributePreset {
  id: string;
  name: string;
  slug: string;
  description?: string;
  isActive: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
  attributes?: PresetAttribute[];
  _count?: {
    attributes: number;
    products: number;
  };
}

export interface PresetAttribute {
  id: string;
  presetId: string;
  attributeId: string;
  isRequired: boolean;
  defaultValueId?: string;
  sortOrder: number;
  createdAt: string;
  preset?: AttributePreset;
  attribute?: Attribute;
  defaultValue?: AttributeValue;
}

export interface AttributePresetFormData {
  name: string;
  slug?: string;
  description?: string;
  isActive?: boolean;
  sortOrder?: number;
  attributes?: PresetAttributeFormData[];
}

export interface PresetAttributeFormData {
  id?: string;
  attributeId: string;
  isRequired?: boolean;
  defaultValueId?: string;
  sortOrder?: number;
}

export interface AttributePresetFilters {
  page: number;
  limit: number;
  search?: string;
  isActive?: boolean;
  sortBy: 'name' | 'sortOrder' | 'createdAt' | 'updatedAt';
  sortOrder: 'asc' | 'desc';
}

export interface AttributePresetStats {
  total: number;
  active: number;
  inactive: number;
  totalAttributes: number;
  totalProducts: number;
}

// API Response types
export interface AttributePresetResponse {
  success: boolean;
  data?: AttributePreset;
  error?: string;
  message?: string;
}

export interface AttributePresetListResponse {
  success: boolean;
  data?: AttributePreset[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  error?: string;
}

export interface AttributePresetStatsResponse {
  success: boolean;
  data?: AttributePresetStats;
  error?: string;
}
