"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import {
  Plus,
  X,
  GripVertical,
  Settings,
  Tag,
  Palette,
  Ruler,
  Type,
  Hash,
  ToggleLeft,
  List,
  ListChecks,
} from "lucide-react";
import { AttributePresetFormData, PresetAttributeFormData } from "@/types/attribute-preset";
import { Attribute, AttributeType, ATTRIBUTE_TYPE_LABELS } from "@/types/attribute";

interface AttributePresetFormProps {
  initialData?: Partial<AttributePresetFormData>;
  onSubmit: (data: AttributePresetFormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  mode?: "create" | "edit";
}

const ATTRIBUTE_TYPE_ICONS: Record<AttributeType, React.ComponentType<any>> = {
  TEXT: Type,
  NUMBER: Hash,
  COLOR: Palette,
  SIZE: Ruler,
  BOOLEAN: ToggleLeft,
  SELECT: List,
  MULTI_SELECT: ListChecks,
};

export function AttributePresetForm({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  mode = "create",
}: AttributePresetFormProps) {
  const [formData, setFormData] = useState<AttributePresetFormData>({
    name: "",
    slug: "",
    description: "",
    isActive: true,
    sortOrder: 0,
    attributes: [],
    ...initialData,
  });

  const [availableAttributes, setAvailableAttributes] = useState<Attribute[]>([]);
  const [loadingAttributes, setLoadingAttributes] = useState(false);

  // Load available attributes
  useEffect(() => {
    const fetchAttributes = async () => {
      setLoadingAttributes(true);
      try {
        const response = await fetch("/api/admin/attributes?limit=100");
        const result = await response.json();

        if (result.success && result.data) {
          setAvailableAttributes(result.data);
        } else {
          toast.error("Không thể tải danh sách thuộc tính");
        }
      } catch (error) {
        console.error("Fetch attributes error:", error);
        toast.error("Có lỗi xảy ra khi tải thuộc tính");
      } finally {
        setLoadingAttributes(false);
      }
    };

    fetchAttributes();
  }, []);

  // Auto-generate slug from name
  useEffect(() => {
    if (formData.name && (!formData.slug || mode === "create")) {
      const slug = formData.name
        .toLowerCase()
        .replace(/\s+/g, "-")
        .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
        .replace(/[èéẹẻẽêềếệểễ]/g, "e")
        .replace(/[ìíịỉĩ]/g, "i")
        .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
        .replace(/[ùúụủũưừứựửữ]/g, "u")
        .replace(/[ỳýỵỷỹ]/g, "y")
        .replace(/[đ]/g, "d")
        .replace(/[^a-z0-9-]/g, "");

      setFormData((prev) => ({ ...prev, slug }));
    }
  }, [formData.name, mode]);

  const handleInputChange = (field: keyof AttributePresetFormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleAddAttribute = () => {
    const newAttribute: PresetAttributeFormData = {
      attributeId: "",
      isRequired: false,
      sortOrder: formData.attributes?.length || 0,
    };

    setFormData((prev) => ({
      ...prev,
      attributes: [...(prev.attributes || []), newAttribute],
    }));
  };

  const handleRemoveAttribute = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      attributes: prev.attributes?.filter((_, i) => i !== index) || [],
    }));
  };

  const handleAttributeChange = (
    index: number,
    field: keyof PresetAttributeFormData,
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      attributes: prev.attributes?.map((attr, i) =>
        i === index ? { ...attr, [field]: value } : attr
      ) || [],
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error("Vui lòng nhập tên preset");
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error("Submit error:", error);
    }
  };

  const getAttributeById = (id: string) => {
    return availableAttributes.find((attr) => attr.id === id);
  };

  const getAttributeIcon = (type: AttributeType) => {
    const Icon = ATTRIBUTE_TYPE_ICONS[type];
    return <Icon className="h-4 w-4" />;
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            Thông tin cơ bản
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Tên preset *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Nhập tên preset"
                required
              />
            </div>

            <div>
              <Label htmlFor="slug">Slug</Label>
              <Input
                id="slug"
                value={formData.slug}
                onChange={(e) => handleInputChange("slug", e.target.value)}
                placeholder="auto-generated-slug"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="description">Mô tả</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder="Mô tả preset (tùy chọn)"
              rows={3}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => handleInputChange("isActive", checked)}
              />
              <Label htmlFor="isActive">Kích hoạt preset</Label>
            </div>

            <div className="w-32">
              <Label htmlFor="sortOrder">Thứ tự</Label>
              <Input
                id="sortOrder"
                type="number"
                value={formData.sortOrder}
                onChange={(e) => handleInputChange("sortOrder", parseInt(e.target.value) || 0)}
                min="0"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Attributes */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <Tag className="h-5 w-5 mr-2" />
              Thuộc tính ({formData.attributes?.length || 0})
            </CardTitle>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleAddAttribute}
              disabled={loadingAttributes}
            >
              <Plus className="h-4 w-4 mr-2" />
              Thêm thuộc tính
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {formData.attributes && formData.attributes.length > 0 ? (
            <div className="space-y-4">
              {formData.attributes.map((presetAttr, index) => {
                const attribute = getAttributeById(presetAttr.attributeId);
                return (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-start gap-4">
                      <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <Label>Thuộc tính *</Label>
                          <Select
                            value={presetAttr.attributeId}
                            onValueChange={(value) =>
                              handleAttributeChange(index, "attributeId", value)
                            }
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Chọn thuộc tính" />
                            </SelectTrigger>
                            <SelectContent>
                              {availableAttributes.map((attr) => (
                                <SelectItem key={attr.id} value={attr.id}>
                                  <div className="flex items-center gap-2">
                                    {getAttributeIcon(attr.type)}
                                    <span>{attr.name}</span>
                                    <Badge variant="secondary" className="text-xs">
                                      {ATTRIBUTE_TYPE_LABELS[attr.type]}
                                    </Badge>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={presetAttr.isRequired}
                              onCheckedChange={(checked) =>
                                handleAttributeChange(index, "isRequired", checked)
                              }
                            />
                            <Label className="text-sm">Bắt buộc</Label>
                          </div>
                        </div>

                        <div>
                          <Label>Thứ tự</Label>
                          <Input
                            type="number"
                            value={presetAttr.sortOrder}
                            onChange={(e) =>
                              handleAttributeChange(
                                index,
                                "sortOrder",
                                parseInt(e.target.value) || 0
                              )
                            }
                            min="0"
                          />
                        </div>
                      </div>

                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveAttribute(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <Tag className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Chưa có thuộc tính nào</p>
              <p className="text-sm">Nhấn "Thêm thuộc tính" để bắt đầu</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex items-center justify-end space-x-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Hủy
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? "Đang xử lý..." : mode === "create" ? "Tạo preset" : "Cập nhật preset"}
        </Button>
      </div>
    </form>
  );
}
