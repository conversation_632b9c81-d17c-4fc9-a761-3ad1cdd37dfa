"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  Settings,
  Tag,
  Eye,
  Info,
  X,
  Palette,
  Ruler,
  Type,
  Hash,
  ToggleLeft,
  List,
  ListChecks,
  Search,
} from "lucide-react";
import { AttributePreset } from "@/types/attribute-preset";
import {
  Attribute,
  AttributeType,
  ATTRIBUTE_TYPE_LABELS,
} from "@/types/attribute";

interface AttributePresetSelectorProps {
  selectedPresetId?: string;
  onPresetChange: (presetId: string | null, preset?: AttributePreset) => void;
  onAttributesChange: (
    attributes: Array<{ attributeId: string; attributeValueId: string }>
  ) => void;
  className?: string;
}

const ATTRIBUTE_TYPE_ICONS: Record<AttributeType, React.ComponentType<any>> = {
  TEXT: Type,
  NUMBER: Hash,
  COLOR: Palette,
  SIZE: Ruler,
  BOOLEAN: ToggleLeft,
  SELECT: List,
  MULTI_SELECT: ListChecks,
};

export function AttributePresetSelector({
  selectedPresetId,
  onPresetChange,
  onAttributesChange,
  className,
}: AttributePresetSelectorProps) {
  const [presets, setPresets] = useState<AttributePreset[]>([]);
  const [selectedPreset, setSelectedPreset] = useState<AttributePreset | null>(
    null
  );
  const [loading, setLoading] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  // Load presets
  useEffect(() => {
    const fetchPresets = async () => {
      setLoading(true);
      try {
        const response = await fetch(
          "/api/admin/attribute-presets?limit=100&isActive=true"
        );
        const result = await response.json();

        if (result.success && result.data) {
          setPresets(result.data);
        } else {
          toast.error("Không thể tải danh sách preset");
        }
      } catch (error) {
        console.error("Fetch presets error:", error);
        toast.error("Có lỗi xảy ra khi tải preset");
      } finally {
        setLoading(false);
      }
    };

    fetchPresets();
  }, []);

  // Load selected preset details
  useEffect(() => {
    if (selectedPresetId) {
      const fetchPresetDetails = async () => {
        try {
          const response = await fetch(
            `/api/admin/attribute-presets/${selectedPresetId}`
          );
          const result = await response.json();

          if (result.success && result.data) {
            setSelectedPreset(result.data);
          }
        } catch (error) {
          console.error("Fetch preset details error:", error);
        }
      };

      fetchPresetDetails();
    } else {
      setSelectedPreset(null);
    }
  }, [selectedPresetId]);

  const handlePresetChange = (presetId: string) => {
    if (presetId === "none") {
      onPresetChange(null);
      onAttributesChange([]);
      setSelectedPreset(null);
      return;
    }

    const preset = presets.find((p) => p.id === presetId);
    if (preset) {
      onPresetChange(presetId, preset);
      setSelectedPreset(preset);

      // Auto-populate attributes with default values
      const defaultAttributes =
        preset.attributes
          ?.filter((attr) => attr.defaultValueId)
          .map((attr) => ({
            attributeId: attr.attributeId,
            attributeValueId: attr.defaultValueId!,
          })) || [];

      onAttributesChange(defaultAttributes);

      // Show success message
      toast.success(
        `Đã áp dụng preset "${preset.name}" với ${defaultAttributes.length} thuộc tính mặc định`
      );
    }
  };

  const getAttributeIcon = (type: AttributeType) => {
    const Icon = ATTRIBUTE_TYPE_ICONS[type];
    return <Icon className="h-4 w-4" />;
  };

  const filteredPresets = presets.filter(
    (preset) =>
      preset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      preset.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            Preset Thuộc tính
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="preset-select">Chọn preset</Label>
            <div className="space-y-3 mt-2">
              {/* Search input */}
              <div className="relative">
                <Input
                  placeholder="Tìm kiếm preset..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              </div>

              {/* Preset selector */}
              <div className="flex gap-2">
                <Select
                  value={selectedPresetId || "none"}
                  onValueChange={handlePresetChange}
                  disabled={loading}
                >
                  <SelectTrigger className="flex-1">
                    <SelectValue placeholder="Chọn preset thuộc tính..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">
                      <div className="flex items-center gap-2">
                        <X className="h-4 w-4 text-gray-400" />
                        <span>Không sử dụng preset</span>
                      </div>
                    </SelectItem>
                    {filteredPresets.map((preset) => (
                      <SelectItem key={preset.id} value={preset.id}>
                        <div className="flex items-center gap-2">
                          <Settings className="h-4 w-4 text-pink-500" />
                          <span>{preset.name}</span>
                          <Badge variant="secondary" className="text-xs">
                            {preset._count?.attributes || 0} thuộc tính
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

              {selectedPreset && (
                <Dialog open={previewOpen} onOpenChange={setPreviewOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-2" />
                      Xem trước
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle className="flex items-center">
                        <Settings className="h-5 w-5 mr-2" />
                        {selectedPreset.name}
                      </DialogTitle>
                      <DialogDescription>
                        {selectedPreset.description ||
                          "Xem trước các thuộc tính trong preset"}
                      </DialogDescription>
                    </DialogHeader>

                    <div className="space-y-4">
                      {selectedPreset.attributes &&
                      selectedPreset.attributes.length > 0 ? (
                        <div className="space-y-3">
                          {selectedPreset.attributes
                            .sort((a, b) => a.sortOrder - b.sortOrder)
                            .map((presetAttr) => (
                              <div
                                key={presetAttr.id}
                                className="border rounded-lg p-3"
                              >
                                <div className="flex items-center justify-between mb-2">
                                  <div className="flex items-center gap-2">
                                    {presetAttr.attribute &&
                                      getAttributeIcon(
                                        presetAttr.attribute.type
                                      )}
                                    <span className="font-medium">
                                      {presetAttr.attribute?.name || "N/A"}
                                    </span>
                                    {presetAttr.attribute && (
                                      <Badge
                                        variant="outline"
                                        className="text-xs"
                                      >
                                        {
                                          ATTRIBUTE_TYPE_LABELS[
                                            presetAttr.attribute.type
                                          ]
                                        }
                                      </Badge>
                                    )}
                                  </div>
                                  {presetAttr.isRequired && (
                                    <Badge
                                      variant="destructive"
                                      className="text-xs"
                                    >
                                      Bắt buộc
                                    </Badge>
                                  )}
                                </div>

                                {presetAttr.defaultValue && (
                                  <div className="text-sm text-muted-foreground">
                                    Giá trị mặc định:{" "}
                                    <Badge
                                      variant="secondary"
                                      className="text-xs"
                                    >
                                      {presetAttr.defaultValue.value}
                                    </Badge>
                                  </div>
                                )}

                                {presetAttr.attribute?.values &&
                                  presetAttr.attribute.values.length > 0 && (
                                    <div className="mt-2">
                                      <div className="text-xs text-muted-foreground mb-1">
                                        Các giá trị có sẵn:
                                      </div>
                                      <div className="flex flex-wrap gap-1">
                                        {presetAttr.attribute.values
                                          .sort(
                                            (a, b) => a.sortOrder - b.sortOrder
                                          )
                                          .map((value) => (
                                            <Badge
                                              key={value.id}
                                              variant={
                                                value.id ===
                                                presetAttr.defaultValueId
                                                  ? "default"
                                                  : "outline"
                                              }
                                              className="text-xs"
                                            >
                                              {value.value}
                                            </Badge>
                                          ))}
                                      </div>
                                    </div>
                                  )}
                              </div>
                            ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-muted-foreground">
                          <Tag className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>Preset này chưa có thuộc tính nào</p>
                        </div>
                      )}
                    </div>
                  </DialogContent>
                </Dialog>
              )}
            </div>
          </div>

          {selectedPreset && (
            <div className="space-y-3">
              <Separator />
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Info className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium">Thông tin preset</span>
                </div>
                <div className="text-sm text-muted-foreground space-y-1">
                  <p>
                    <strong>Mô tả:</strong>{" "}
                    {selectedPreset.description || "Không có mô tả"}
                  </p>
                  <p>
                    <strong>Số thuộc tính:</strong>{" "}
                    {selectedPreset._count?.attributes || 0}
                  </p>
                  <p>
                    <strong>Được sử dụng bởi:</strong>{" "}
                    {selectedPreset._count?.products || 0} sản phẩm
                  </p>
                </div>
              </div>

              {selectedPreset.attributes &&
                selectedPreset.attributes.length > 0 && (
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <Tag className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">
                        Thuộc tính sẽ được áp dụng
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {selectedPreset.attributes
                        .sort((a, b) => a.sortOrder - b.sortOrder)
                        .map((presetAttr) => (
                          <Badge
                            key={presetAttr.id}
                            variant={
                              presetAttr.isRequired
                                ? "destructive"
                                : "secondary"
                            }
                            className="text-xs"
                          >
                            {presetAttr.attribute?.name || "N/A"}
                            {presetAttr.isRequired && " *"}
                          </Badge>
                        ))}
                    </div>
                  </div>
                )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
