"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  Tag,
  Plus,
  X,
  Palette,
  Ruler,
  Type,
  Hash,
  ToggleLeft,
  List,
  ListChecks,
  AlertCircle,
} from "lucide-react";
import { AttributePreset } from "@/types/attribute-preset";
import {
  Attribute,
  AttributeValue,
  AttributeType,
  ATTRIBUTE_TYPE_LABELS,
} from "@/types/attribute";

interface ProductAttribute {
  attributeId: string;
  attributeValueId: string;
}

interface ProductAttributeManagerProps {
  preset?: AttributePreset | null;
  attributes: ProductAttribute[];
  onAttributesChange: (attributes: ProductAttribute[]) => void;
  className?: string;
}

const ATTRIBUTE_TYPE_ICONS: Record<AttributeType, React.ComponentType<any>> = {
  TEXT: Type,
  NUMBER: Hash,
  COLOR: Palette,
  SIZE: Ruler,
  BOOLEAN: ToggleLeft,
  SELECT: List,
  MULTI_SELECT: ListChecks,
};

export function ProductAttributeManager({
  preset,
  attributes,
  onAttributesChange,
  className,
}: ProductAttributeManagerProps) {
  const [availableAttributes, setAvailableAttributes] = useState<Attribute[]>(
    []
  );
  const [loading, setLoading] = useState(false);
  const [customAttributes, setCustomAttributes] = useState<ProductAttribute[]>(
    []
  );

  // Load available attributes
  useEffect(() => {
    const fetchAttributes = async () => {
      setLoading(true);
      try {
        const response = await fetch("/api/admin/attributes?limit=100");
        const result = await response.json();

        if (result.success && result.data) {
          setAvailableAttributes(result.data);
        } else {
          toast.error("Không thể tải danh sách thuộc tính");
        }
      } catch (error) {
        console.error("Fetch attributes error:", error);
        toast.error("Có lỗi xảy ra khi tải thuộc tính");
      } finally {
        setLoading(false);
      }
    };

    fetchAttributes();
  }, []);

  // Separate preset and custom attributes
  useEffect(() => {
    if (preset) {
      const presetAttributeIds =
        preset.attributes?.map((attr) => attr.attributeId) || [];
      const custom = attributes.filter(
        (attr) => !presetAttributeIds.includes(attr.attributeId)
      );
      setCustomAttributes(custom);
    } else {
      setCustomAttributes(attributes);
    }
  }, [preset, attributes]);

  const getAttributeById = (id: string) => {
    return availableAttributes.find((attr) => attr.id === id);
  };

  const getAttributeIcon = (type: AttributeType) => {
    const Icon = ATTRIBUTE_TYPE_ICONS[type];
    return <Icon className="h-4 w-4" />;
  };

  const handlePresetAttributeChange = (
    attributeId: string,
    valueId: string
  ) => {
    const newAttributes = attributes.filter(
      (attr) => attr.attributeId !== attributeId
    );
    if (valueId) {
      newAttributes.push({ attributeId, attributeValueId: valueId });
    }
    onAttributesChange(newAttributes);
  };

  const handleAddCustomAttribute = () => {
    const newAttribute: ProductAttribute = {
      attributeId: "",
      attributeValueId: "",
    };
    setCustomAttributes([...customAttributes, newAttribute]);
  };

  const handleRemoveCustomAttribute = (index: number) => {
    const newCustom = customAttributes.filter((_, i) => i !== index);
    setCustomAttributes(newCustom);

    // Update main attributes list
    const presetAttributeIds =
      preset?.attributes?.map((attr) => attr.attributeId) || [];
    const presetAttributes = attributes.filter((attr) =>
      presetAttributeIds.includes(attr.attributeId)
    );
    onAttributesChange([...presetAttributes, ...newCustom]);
  };

  const handleCustomAttributeChange = (
    index: number,
    field: keyof ProductAttribute,
    value: string
  ) => {
    const newCustom = customAttributes.map((attr, i) =>
      i === index ? { ...attr, [field]: value } : attr
    );
    setCustomAttributes(newCustom);

    // Update main attributes list
    const presetAttributeIds =
      preset?.attributes?.map((attr) => attr.attributeId) || [];
    const presetAttributes = attributes.filter((attr) =>
      presetAttributeIds.includes(attr.attributeId)
    );
    onAttributesChange([...presetAttributes, ...newCustom]);
  };

  const validateAttributes = () => {
    const errors: string[] = [];

    // Check required preset attributes
    if (preset?.attributes) {
      preset.attributes.forEach((presetAttr) => {
        if (presetAttr.isRequired) {
          const hasValue = attributes.some(
            (attr) =>
              attr.attributeId === presetAttr.attributeId &&
              attr.attributeValueId
          );
          if (!hasValue) {
            const attribute = getAttributeById(presetAttr.attributeId);
            errors.push(`Thuộc tính "${attribute?.name || "N/A"}" là bắt buộc`);
          }
        }
      });
    }

    // Check custom attributes completeness
    customAttributes.forEach((attr, index) => {
      if (attr.attributeId && !attr.attributeValueId) {
        errors.push(`Thuộc tính tùy chỉnh #${index + 1} chưa có giá trị`);
      }
      if (!attr.attributeId && attr.attributeValueId) {
        errors.push(
          `Thuộc tính tùy chỉnh #${index + 1} chưa chọn loại thuộc tính`
        );
      }
    });

    return errors;
  };

  const getSelectedValueId = (attributeId: string) => {
    const attr = attributes.find((a) => a.attributeId === attributeId);
    return attr?.attributeValueId || "";
  };

  const getAvailableCustomAttributes = () => {
    const usedAttributeIds = attributes.map((attr) => attr.attributeId);
    return availableAttributes.filter(
      (attr) => !usedAttributeIds.includes(attr.id)
    );
  };

  const renderAttributeInput = (
    attribute: Attribute,
    value: string,
    onChange: (value: string) => void
  ) => {
    if (!attribute.values || attribute.values.length === 0) {
      // For attributes without predefined values (TEXT, NUMBER, BOOLEAN)
      switch (attribute.type) {
        case "TEXT":
          return (
            <Input
              value={value}
              onChange={(e) => onChange(e.target.value)}
              placeholder="Nhập giá trị..."
            />
          );
        case "NUMBER":
          return (
            <Input
              type="number"
              value={value}
              onChange={(e) => onChange(e.target.value)}
              placeholder="Nhập số..."
            />
          );
        case "BOOLEAN":
          return (
            <Switch
              checked={value === "true"}
              onCheckedChange={(checked) =>
                onChange(checked ? "true" : "false")
              }
            />
          );
        default:
          return (
            <Input
              value={value}
              onChange={(e) => onChange(e.target.value)}
              placeholder="Nhập giá trị..."
            />
          );
      }
    }

    // For attributes with predefined values
    return (
      <Select value={value} onValueChange={onChange}>
        <SelectTrigger>
          <SelectValue placeholder="Chọn giá trị..." />
        </SelectTrigger>
        <SelectContent>
          {attribute.values
            .sort((a, b) => a.sortOrder - b.sortOrder)
            .map((val) => (
              <SelectItem key={val.id} value={val.id}>
                {val.value}
              </SelectItem>
            ))}
        </SelectContent>
      </Select>
    );
  };

  const validationErrors = validateAttributes();

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <Tag className="h-5 w-5 mr-2" />
              Thuộc tính sản phẩm
            </div>
            {validationErrors.length > 0 && (
              <Badge variant="destructive" className="text-xs">
                <AlertCircle className="h-3 w-3 mr-1" />
                {validationErrors.length} lỗi
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <span className="text-sm font-medium text-red-800">
                  Cần khắc phục các lỗi sau:
                </span>
              </div>
              <ul className="text-sm text-red-700 space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-red-400 mt-1">•</span>
                    <span>{error}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
          {/* Preset Attributes */}
          {preset && preset.attributes && preset.attributes.length > 0 && (
            <div>
              <div className="flex items-center gap-2 mb-4">
                <Badge
                  variant="default"
                  className="text-xs bg-pink-100 text-pink-800 border-pink-200"
                >
                  Từ preset: {preset.name}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  ({preset.attributes.length} thuộc tính)
                </span>
              </div>
              <div className="space-y-4">
                {preset.attributes
                  .sort((a, b) => a.sortOrder - b.sortOrder)
                  .map((presetAttr) => {
                    const attribute = getAttributeById(presetAttr.attributeId);
                    if (!attribute) return null;

                    return (
                      <div
                        key={presetAttr.id}
                        className="border rounded-lg p-4"
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2">
                            {getAttributeIcon(attribute.type)}
                            <span className="font-medium">
                              {attribute.name}
                            </span>
                            <Badge variant="outline" className="text-xs">
                              {ATTRIBUTE_TYPE_LABELS[attribute.type]}
                            </Badge>
                            {presetAttr.isRequired && (
                              <Badge variant="destructive" className="text-xs">
                                Bắt buộc
                              </Badge>
                            )}
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label>Giá trị</Label>
                          {renderAttributeInput(
                            attribute,
                            getSelectedValueId(attribute.id),
                            (value) =>
                              handlePresetAttributeChange(attribute.id, value)
                          )}
                        </div>

                        {presetAttr.isRequired &&
                          !getSelectedValueId(attribute.id) && (
                            <div className="flex items-center gap-2 mt-2 text-red-600 text-sm">
                              <AlertCircle className="h-4 w-4" />
                              <span>Thuộc tính này là bắt buộc</span>
                            </div>
                          )}
                      </div>
                    );
                  })}
              </div>
            </div>
          )}

          {/* Separator */}
          {preset &&
            preset.attributes &&
            preset.attributes.length > 0 &&
            customAttributes.length > 0 && <Separator />}

          {/* Custom Attributes */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Badge
                  variant="secondary"
                  className="text-xs bg-blue-100 text-blue-800 border-blue-200"
                >
                  Thuộc tính tùy chỉnh
                </Badge>
                {customAttributes.length > 0 && (
                  <span className="text-xs text-muted-foreground">
                    ({customAttributes.length} thuộc tính)
                  </span>
                )}
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddCustomAttribute}
                disabled={
                  loading || getAvailableCustomAttributes().length === 0
                }
                className="hover:bg-blue-50 hover:border-blue-300"
              >
                <Plus className="h-4 w-4 mr-2" />
                Thêm thuộc tính
              </Button>
            </div>

            {customAttributes.length > 0 ? (
              <div className="space-y-4">
                {customAttributes.map((customAttr, index) => {
                  const attribute = getAttributeById(customAttr.attributeId);

                  return (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-start gap-4">
                        <div className="flex-1 space-y-4">
                          <div>
                            <Label>Thuộc tính</Label>
                            <Select
                              value={customAttr.attributeId}
                              onValueChange={(value) =>
                                handleCustomAttributeChange(
                                  index,
                                  "attributeId",
                                  value
                                )
                              }
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Chọn thuộc tính..." />
                              </SelectTrigger>
                              <SelectContent>
                                {getAvailableCustomAttributes().map((attr) => (
                                  <SelectItem key={attr.id} value={attr.id}>
                                    <div className="flex items-center gap-2">
                                      {getAttributeIcon(attr.type)}
                                      <span>{attr.name}</span>
                                      <Badge
                                        variant="outline"
                                        className="text-xs"
                                      >
                                        {ATTRIBUTE_TYPE_LABELS[attr.type]}
                                      </Badge>
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          {attribute && (
                            <div>
                              <Label>Giá trị</Label>
                              {renderAttributeInput(
                                attribute,
                                customAttr.attributeValueId,
                                (value) =>
                                  handleCustomAttributeChange(
                                    index,
                                    "attributeValueId",
                                    value
                                  )
                              )}
                            </div>
                          )}
                        </div>

                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveCustomAttribute(index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Tag className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Chưa có thuộc tính tùy chỉnh nào</p>
                <p className="text-sm">Nhấn "Thêm thuộc tính" để bắt đầu</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
