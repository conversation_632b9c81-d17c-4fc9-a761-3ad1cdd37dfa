"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON><PERSON>, Settings, Loader2 } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { AttributePresetForm } from "@/components/admin/attribute-presets/AttributePresetForm";
import { AttributePreset, AttributePresetFormData } from "@/types/attribute-preset";

interface EditAttributePresetPageProps {
  params: { id: string };
}

export default function EditAttributePresetPage({ params }: EditAttributePresetPageProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [preset, setPreset] = useState<AttributePreset | null>(null);

  // Load preset data
  useEffect(() => {
    const fetchPreset = async () => {
      setLoading(true);
      try {
        const response = await fetch(`/api/admin/attribute-presets/${params.id}`);
        const result = await response.json();

        if (result.success && result.data) {
          setPreset(result.data);
        } else {
          toast.error(result.error || "Không thể tải thông tin preset");
          router.push("/admin/attribute-presets");
        }
      } catch (error) {
        console.error("Fetch preset error:", error);
        toast.error("Có lỗi xảy ra khi tải thông tin preset");
        router.push("/admin/attribute-presets");
      } finally {
        setLoading(false);
      }
    };

    fetchPreset();
  }, [params.id, router]);

  const handleSubmit = async (data: AttributePresetFormData) => {
    setSaving(true);
    try {
      const response = await fetch(`/api/admin/attribute-presets/${params.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success) {
        toast.success(result.message || "Preset đã được cập nhật thành công");
        router.push("/admin/attribute-presets");
      } else {
        toast.error(result.error || "Có lỗi xảy ra khi cập nhật preset");
      }
    } catch (error) {
      console.error("Update preset error:", error);
      toast.error("Có lỗi xảy ra khi cập nhật preset");
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    router.push("/admin/attribute-presets");
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (!preset) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <p className="text-muted-foreground">Không tìm thấy preset</p>
          <Link href="/admin/attribute-presets">
            <Button className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại danh sách
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  // Convert preset data to form data
  const initialData: Partial<AttributePresetFormData> = {
    name: preset.name,
    slug: preset.slug,
    description: preset.description || "",
    isActive: preset.isActive,
    sortOrder: preset.sortOrder,
    attributes: preset.attributes?.map((attr) => ({
      id: attr.id,
      attributeId: attr.attributeId,
      isRequired: attr.isRequired,
      defaultValueId: attr.defaultValueId || undefined,
      sortOrder: attr.sortOrder,
    })) || [],
  };

  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Link href="/admin/attribute-presets">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">Chỉnh sửa Preset: {preset.name}</h1>
          <p className="text-muted-foreground">
            Cập nhật thông tin và thuộc tính của preset
          </p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            Thông tin preset
          </CardTitle>
        </CardHeader>
        <CardContent>
          <AttributePresetForm
            initialData={initialData}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            loading={saving}
            mode="edit"
          />
        </CardContent>
      </Card>
    </div>
  );
}
