"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ArrowLeft,
  Settings,
  Edit,
  Trash2,
  Tag,
  Package,
  Eye,
  EyeOff,
  Loader2,
  Calendar,
  Hash,
} from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { AttributePreset } from "@/types/attribute-preset";
import { ATTRIBUTE_TYPE_LABELS } from "@/types/attribute";

interface AttributePresetDetailPageProps {
  params: { id: string };
}

export default function AttributePresetDetailPage({ params }: AttributePresetDetailPageProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [preset, setPreset] = useState<AttributePreset | null>(null);

  // Load preset data
  useEffect(() => {
    const fetchPreset = async () => {
      setLoading(true);
      try {
        const response = await fetch(`/api/admin/attribute-presets/${params.id}`);
        const result = await response.json();

        if (result.success && result.data) {
          setPreset(result.data);
        } else {
          toast.error(result.error || "Không thể tải thông tin preset");
          router.push("/admin/attribute-presets");
        }
      } catch (error) {
        console.error("Fetch preset error:", error);
        toast.error("Có lỗi xảy ra khi tải thông tin preset");
        router.push("/admin/attribute-presets");
      } finally {
        setLoading(false);
      }
    };

    fetchPreset();
  }, [params.id, router]);

  const handleDelete = async () => {
    if (!preset) return;

    if (confirm(`Bạn có chắc chắn muốn xóa preset "${preset.name}"?`)) {
      try {
        const response = await fetch(`/api/admin/attribute-presets/${preset.id}`, {
          method: "DELETE",
        });

        const result = await response.json();

        if (result.success) {
          toast.success(result.message || "Preset đã được xóa thành công");
          router.push("/admin/attribute-presets");
        } else {
          toast.error(result.error || "Có lỗi xảy ra khi xóa preset");
        }
      } catch (error) {
        console.error("Delete preset error:", error);
        toast.error("Có lỗi xảy ra khi xóa preset");
      }
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (!preset) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <p className="text-muted-foreground">Không tìm thấy preset</p>
          <Link href="/admin/attribute-presets">
            <Button className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại danh sách
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Link href="/admin/attribute-presets">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">{preset.name}</h1>
            <p className="text-muted-foreground">Chi tiết preset thuộc tính</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Link href={`/admin/attribute-presets/${preset.id}/edit`}>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              Chỉnh sửa
            </Button>
          </Link>
          <Button variant="destructive" onClick={handleDelete}>
            <Trash2 className="h-4 w-4 mr-2" />
            Xóa
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Basic Information */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2" />
                Thông tin cơ bản
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Tên preset</label>
                  <p className="text-lg font-semibold">{preset.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Slug</label>
                  <p className="text-lg font-mono">{preset.slug}</p>
                </div>
              </div>

              {preset.description && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Mô tả</label>
                  <p className="text-base">{preset.description}</p>
                </div>
              )}

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Trạng thái</label>
                  <div className="mt-1">
                    <Badge variant={preset.isActive ? "default" : "secondary"}>
                      {preset.isActive ? (
                        <>
                          <Eye className="h-3 w-3 mr-1" />
                          Hoạt động
                        </>
                      ) : (
                        <>
                          <EyeOff className="h-3 w-3 mr-1" />
                          Vô hiệu
                        </>
                      )}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Thứ tự</label>
                  <p className="text-lg font-semibold">{preset.sortOrder}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Ngày tạo</label>
                  <p className="text-base">
                    {new Date(preset.createdAt).toLocaleDateString("vi-VN")}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Attributes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Tag className="h-5 w-5 mr-2" />
                Thuộc tính ({preset.attributes?.length || 0})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {preset.attributes && preset.attributes.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Tên thuộc tính</TableHead>
                      <TableHead>Loại</TableHead>
                      <TableHead>Bắt buộc</TableHead>
                      <TableHead>Giá trị mặc định</TableHead>
                      <TableHead>Thứ tự</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {preset.attributes
                      .sort((a, b) => a.sortOrder - b.sortOrder)
                      .map((presetAttr) => (
                        <TableRow key={presetAttr.id}>
                          <TableCell>
                            <div className="font-medium">
                              {presetAttr.attribute?.name || "N/A"}
                            </div>
                          </TableCell>
                          <TableCell>
                            {presetAttr.attribute && (
                              <Badge variant="outline">
                                {ATTRIBUTE_TYPE_LABELS[presetAttr.attribute.type]}
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            {presetAttr.isRequired ? (
                              <Badge variant="destructive" className="text-xs">
                                Bắt buộc
                              </Badge>
                            ) : (
                              <Badge variant="secondary" className="text-xs">
                                Tùy chọn
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            {presetAttr.defaultValue ? (
                              <Badge variant="outline" className="text-xs">
                                {presetAttr.defaultValue.value}
                              </Badge>
                            ) : (
                              <span className="text-muted-foreground text-sm">Không có</span>
                            )}
                          </TableCell>
                          <TableCell>{presetAttr.sortOrder}</TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Tag className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Chưa có thuộc tính nào</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Statistics */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Hash className="h-5 w-5 mr-2" />
                Thống kê
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Số thuộc tính</span>
                <span className="font-semibold">{preset._count?.attributes || 0}</span>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Sản phẩm sử dụng</span>
                <span className="font-semibold">{preset._count?.products || 0}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Thông tin thời gian
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Ngày tạo</label>
                <p className="text-sm">
                  {new Date(preset.createdAt).toLocaleString("vi-VN")}
                </p>
              </div>
              <Separator />
              <div>
                <label className="text-sm font-medium text-muted-foreground">Cập nhật lần cuối</label>
                <p className="text-sm">
                  {new Date(preset.updatedAt).toLocaleString("vi-VN")}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
