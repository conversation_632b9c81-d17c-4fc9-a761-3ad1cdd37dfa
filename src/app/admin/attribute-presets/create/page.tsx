"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Settings } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { AttributePresetForm } from "@/components/admin/attribute-presets/AttributePresetForm";
import { AttributePresetFormData } from "@/types/attribute-preset";

export default function CreateAttributePresetPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (data: AttributePresetFormData) => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/attribute-presets", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success) {
        toast.success(result.message || "Preset đã được tạo thành công");
        router.push("/admin/attribute-presets");
      } else {
        toast.error(result.error || "Có lỗi xảy ra khi tạo preset");
      }
    } catch (error) {
      console.error("Create preset error:", error);
      toast.error("Có lỗi xảy ra khi tạo preset");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.push("/admin/attribute-presets");
  };

  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Link href="/admin/attribute-presets">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">Tạo Preset Thuộc tính</h1>
          <p className="text-muted-foreground">
            Tạo preset mới để nhóm các thuộc tính thường dùng
          </p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            Thông tin preset
          </CardTitle>
        </CardHeader>
        <CardContent>
          <AttributePresetForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            loading={loading}
            mode="create"
          />
        </CardContent>
      </Card>
    </div>
  );
}
