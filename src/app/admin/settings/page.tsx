"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Save, RefreshCw, RotateCcw } from "lucide-react";
import { toast } from "sonner";

interface SiteSettings {
  siteName: string;
  siteDescription: string;
  siteUrl: string;
  logo: string;
  favicon: string;
  contactEmail: string;
  contactPhone: string;
  address: string;
  // SEO Settings
  seoSettings: {
    metaTitle: string;
    metaDescription: string;
    metaKeywords: string;
    ogTitle: string;
    ogDescription: string;
    ogImage: string;
    twitterCard: string;
    twitterSite: string;
    googleAnalyticsId: string;
    googleTagManagerId: string;
    facebookPixelId: string;
    robotsTxt: string;
    sitemapUrl: string;
  };
  socialMedia: {
    facebook: string;
    instagram: string;
    twitter: string;
    youtube: string;
    linkedin: string;
    tiktok: string;
  };
  paymentMethods: {
    cod: boolean;
    bankTransfer: boolean;
    creditCard: boolean;
  };
  shippingSettings: {
    freeShippingThreshold: number;
    shippingFee: number;
    estimatedDelivery: string;
  };
  emailSettings: {
    smtpHost: string;
    smtpPort: number;
    smtpUser: string;
    smtpPassword: string;
    fromEmail: string;
    fromName: string;
  };
  notifications: {
    orderNotifications: boolean;
    stockAlerts: boolean;
    customerNotifications: boolean;
  };
}

export default function AdminSettingsPage() {
  const [settings, setSettings] = useState<SiteSettings>({
    siteName: "",
    siteDescription: "",
    siteUrl: "",
    logo: "",
    favicon: "",
    contactEmail: "",
    contactPhone: "",
    address: "",
    // SEO Settings
    seoSettings: {
      metaTitle: "",
      metaDescription: "",
      metaKeywords: "",
      ogTitle: "",
      ogDescription: "",
      ogImage: "",
      twitterCard: "summary_large_image",
      twitterSite: "",
      googleAnalyticsId: "",
      googleTagManagerId: "",
      facebookPixelId: "",
      robotsTxt: "User-agent: *\nDisallow:",
      sitemapUrl: "/sitemap.xml",
    },
    socialMedia: {
      facebook: "",
      instagram: "",
      twitter: "",
      youtube: "",
      linkedin: "",
      tiktok: "",
    },
    paymentMethods: {
      cod: true,
      bankTransfer: true,
      creditCard: false,
    },
    shippingSettings: {
      freeShippingThreshold: 500000,
      shippingFee: 30000,
      estimatedDelivery: "2-3 ngày",
    },
    emailSettings: {
      smtpHost: "",
      smtpPort: 587,
      smtpUser: "",
      smtpPassword: "",
      fromEmail: "",
      fromName: "",
    },
    notifications: {
      orderNotifications: true,
      stockAlerts: true,
      customerNotifications: true,
    },
  });

  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  // Remove activeTab state since we only have one tab now

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    setInitialLoading(true);
    try {
      const response = await fetch("/api/admin/settings");
      const data = await response.json();

      if (response.ok) {
        setSettings((prev) => ({ ...prev, ...data }));
      } else {
        toast.error("Có lỗi xảy ra khi tải cài đặt");
      }
    } catch {
      toast.error("Có lỗi xảy ra khi tải cài đặt");
    } finally {
      setInitialLoading(false);
    }
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/settings", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ settings }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Cài đặt đã được lưu thành công");
      } else {
        toast.error(data.error || "Có lỗi xảy ra khi lưu cài đặt");
      }
    } catch {
      toast.error("Có lỗi xảy ra khi lưu cài đặt");
    } finally {
      setLoading(false);
    }
  };

  const handleReset = async () => {
    if (
      !confirm(
        "Bạn có chắc chắn muốn khôi phục cài đặt mặc định? Tất cả thay đổi sẽ bị mất."
      )
    ) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch("/api/admin/settings?action=reset", {
        method: "POST",
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Đã khôi phục cài đặt mặc định");
        await fetchSettings();
      } else {
        toast.error(data.error || "Có lỗi xảy ra khi khôi phục cài đặt");
      }
    } catch {
      toast.error("Có lỗi xảy ra khi khôi phục cài đặt");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (section: string, field: string, value: any) => {
    setSettings((prev) => {
      const currentSection = prev[section as keyof SiteSettings];
      return {
        ...prev,
        [section]: {
          ...(typeof currentSection === "object" && currentSection !== null
            ? currentSection
            : {}),
          [field]: value,
        },
      };
    });
  };

  const handleDirectChange = (field: string, value: any) => {
    setSettings((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Remove tabs array since we only have general settings now

  if (initialLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Cài đặt hệ thống</h1>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <div className="lg:col-span-1">
            <Card>
              <CardContent className="p-0">
                <div className="space-y-1">
                  {Array.from({ length: 8 }, (_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-12 bg-gray-200 rounded mx-4 my-2" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
          <div className="lg:col-span-3">
            <Card>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-4">
                  {Array.from({ length: 6 }, (_, i) => (
                    <div key={i} className="h-4 bg-gray-200 rounded" />
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  const renderSettings = () => {
    return (
      <div className="space-y-8">
        {/* Basic Information */}
        <div className="space-y-6">
          <h3 className="text-lg font-semibold">Thông tin cơ bản</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium mb-2">
                Tên website
              </label>
              <input
                type="text"
                value={settings.siteName}
                onChange={(e) => handleDirectChange("siteName", e.target.value)}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                URL website
              </label>
              <input
                type="url"
                value={settings.siteUrl}
                onChange={(e) => handleDirectChange("siteUrl", e.target.value)}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">
              Mô tả website
            </label>
            <textarea
              value={settings.siteDescription}
              onChange={(e) =>
                handleDirectChange("siteDescription", e.target.value)
              }
              rows={3}
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Contact Information */}
        <div className="space-y-6">
          <h3 className="text-lg font-semibold">Thông tin liên hệ</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium mb-2">
                Email liên hệ
              </label>
              <input
                type="email"
                value={settings.contactEmail}
                onChange={(e) =>
                  handleDirectChange("contactEmail", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                Số điện thoại
              </label>
              <input
                type="tel"
                value={settings.contactPhone}
                onChange={(e) =>
                  handleDirectChange("contactPhone", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Địa chỉ</label>
            <textarea
              value={settings.address}
              onChange={(e) => handleDirectChange("address", e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* SEO Settings */}
        <div className="space-y-6">
          <h3 className="text-lg font-semibold">Cài đặt SEO</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium mb-2">
                Meta Title
              </label>
              <input
                type="text"
                value={settings.seoSettings.metaTitle}
                onChange={(e) =>
                  handleInputChange("seoSettings", "metaTitle", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                placeholder="Tiêu đề SEO cho trang chủ"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                Meta Keywords
              </label>
              <input
                type="text"
                value={settings.seoSettings.metaKeywords}
                onChange={(e) =>
                  handleInputChange(
                    "seoSettings",
                    "metaKeywords",
                    e.target.value
                  )
                }
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                placeholder="từ khóa, SEO, website"
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">
              Meta Description
            </label>
            <textarea
              value={settings.seoSettings.metaDescription}
              onChange={(e) =>
                handleInputChange(
                  "seoSettings",
                  "metaDescription",
                  e.target.value
                )
              }
              rows={3}
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              placeholder="Mô tả ngắn gọn về website cho SEO"
            />
          </div>

          {/* Open Graph Settings */}
          <div className="border-t pt-6">
            <h4 className="text-md font-medium mb-4">Open Graph (Facebook)</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium mb-2">
                  OG Title
                </label>
                <input
                  type="text"
                  value={settings.seoSettings.ogTitle}
                  onChange={(e) =>
                    handleInputChange("seoSettings", "ogTitle", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  placeholder="Tiêu đề khi chia sẻ trên Facebook"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">
                  OG Image URL
                </label>
                <input
                  type="url"
                  value={settings.seoSettings.ogImage}
                  onChange={(e) =>
                    handleInputChange("seoSettings", "ogImage", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  placeholder="https://example.com/og-image.jpg"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                OG Description
              </label>
              <textarea
                value={settings.seoSettings.ogDescription}
                onChange={(e) =>
                  handleInputChange(
                    "seoSettings",
                    "ogDescription",
                    e.target.value
                  )
                }
                rows={2}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                placeholder="Mô tả khi chia sẻ trên Facebook"
              />
            </div>
          </div>

          {/* Twitter Settings */}
          <div className="border-t pt-6">
            <h4 className="text-md font-medium mb-4">Twitter Card</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Twitter Card Type
                </label>
                <select
                  value={settings.seoSettings.twitterCard}
                  onChange={(e) =>
                    handleInputChange(
                      "seoSettings",
                      "twitterCard",
                      e.target.value
                    )
                  }
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                >
                  <option value="summary">Summary</option>
                  <option value="summary_large_image">
                    Summary Large Image
                  </option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">
                  Twitter Site
                </label>
                <input
                  type="text"
                  value={settings.seoSettings.twitterSite}
                  onChange={(e) =>
                    handleInputChange(
                      "seoSettings",
                      "twitterSite",
                      e.target.value
                    )
                  }
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  placeholder="@username"
                />
              </div>
            </div>
          </div>

          {/* Analytics Settings */}
          <div className="border-t pt-6">
            <h4 className="text-md font-medium mb-4">Analytics & Tracking</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Google Analytics ID
                </label>
                <input
                  type="text"
                  value={settings.seoSettings.googleAnalyticsId}
                  onChange={(e) =>
                    handleInputChange(
                      "seoSettings",
                      "googleAnalyticsId",
                      e.target.value
                    )
                  }
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  placeholder="G-XXXXXXXXXX"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">
                  Google Tag Manager ID
                </label>
                <input
                  type="text"
                  value={settings.seoSettings.googleTagManagerId}
                  onChange={(e) =>
                    handleInputChange(
                      "seoSettings",
                      "googleTagManagerId",
                      e.target.value
                    )
                  }
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  placeholder="GTM-XXXXXXX"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">
                  Facebook Pixel ID
                </label>
                <input
                  type="text"
                  value={settings.seoSettings.facebookPixelId}
                  onChange={(e) =>
                    handleInputChange(
                      "seoSettings",
                      "facebookPixelId",
                      e.target.value
                    )
                  }
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  placeholder="123456789012345"
                />
              </div>
            </div>
          </div>

          {/* Technical SEO */}
          <div className="border-t pt-6">
            <h4 className="text-md font-medium mb-4">Technical SEO</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Sitemap URL
                </label>
                <input
                  type="url"
                  value={settings.seoSettings.sitemapUrl}
                  onChange={(e) =>
                    handleInputChange(
                      "seoSettings",
                      "sitemapUrl",
                      e.target.value
                    )
                  }
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  placeholder="/sitemap.xml"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                Robots.txt Content
              </label>
              <textarea
                value={settings.seoSettings.robotsTxt}
                onChange={(e) =>
                  handleInputChange("seoSettings", "robotsTxt", e.target.value)
                }
                rows={4}
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                placeholder="User-agent: *&#10;Disallow:"
              />
            </div>
          </div>
        </div>

        {/* Payment Methods */}
        <div className="space-y-6">
          <h3 className="text-lg font-semibold">Phương thức thanh toán</h3>
          <div className="space-y-4">
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={settings.paymentMethods.cod}
                onChange={(e) =>
                  handleInputChange("paymentMethods", "cod", e.target.checked)
                }
                className="rounded border-gray-300 text-pink-600 focus:ring-pink-500"
              />
              <span>Thanh toán khi nhận hàng (COD)</span>
            </label>
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={settings.paymentMethods.bankTransfer}
                onChange={(e) =>
                  handleInputChange(
                    "paymentMethods",
                    "bankTransfer",
                    e.target.checked
                  )
                }
                className="rounded border-gray-300 text-pink-600 focus:ring-pink-500"
              />
              <span>Chuyển khoản ngân hàng</span>
            </label>
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={settings.paymentMethods.creditCard}
                onChange={(e) =>
                  handleInputChange(
                    "paymentMethods",
                    "creditCard",
                    e.target.checked
                  )
                }
                className="rounded border-gray-300 text-pink-600 focus:ring-pink-500"
              />
              <span>Thẻ tín dụng</span>
            </label>
          </div>
        </div>

        {/* Shipping Settings */}
        <div className="space-y-6">
          <h3 className="text-lg font-semibold">Cài đặt vận chuyển</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium mb-2">
                Phí vận chuyển (VND)
              </label>
              <input
                type="number"
                value={settings.shippingSettings.shippingFee}
                onChange={(e) =>
                  handleInputChange(
                    "shippingSettings",
                    "shippingFee",
                    parseInt(e.target.value)
                  )
                }
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                Miễn phí vận chuyển từ (VND)
              </label>
              <input
                type="number"
                value={settings.shippingSettings.freeShippingThreshold}
                onChange={(e) =>
                  handleInputChange(
                    "shippingSettings",
                    "freeShippingThreshold",
                    parseInt(e.target.value)
                  )
                }
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">
              Thời gian giao hàng dự kiến
            </label>
            <input
              type="text"
              value={settings.shippingSettings.estimatedDelivery}
              onChange={(e) =>
                handleInputChange(
                  "shippingSettings",
                  "estimatedDelivery",
                  e.target.value
                )
              }
              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              placeholder="2-3 ngày"
            />
          </div>
        </div>

        {/* Social Media */}
        <div className="space-y-6">
          <h3 className="text-lg font-semibold">Mạng xã hội</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium mb-2">Facebook</label>
              <input
                type="url"
                value={settings.socialMedia.facebook}
                onChange={(e) =>
                  handleInputChange("socialMedia", "facebook", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                placeholder="https://facebook.com/yourpage"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">
                Instagram
              </label>
              <input
                type="url"
                value={settings.socialMedia.instagram}
                onChange={(e) =>
                  handleInputChange("socialMedia", "instagram", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                placeholder="https://instagram.com/yourpage"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Twitter</label>
              <input
                type="url"
                value={settings.socialMedia.twitter}
                onChange={(e) =>
                  handleInputChange("socialMedia", "twitter", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                placeholder="https://twitter.com/yourpage"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">YouTube</label>
              <input
                type="url"
                value={settings.socialMedia.youtube}
                onChange={(e) =>
                  handleInputChange("socialMedia", "youtube", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                placeholder="https://youtube.com/yourchannel"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">LinkedIn</label>
              <input
                type="url"
                value={settings.socialMedia.linkedin}
                onChange={(e) =>
                  handleInputChange("socialMedia", "linkedin", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                placeholder="https://linkedin.com/company/yourcompany"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">TikTok</label>
              <input
                type="url"
                value={settings.socialMedia.tiktok}
                onChange={(e) =>
                  handleInputChange("socialMedia", "tiktok", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                placeholder="https://tiktok.com/@yourpage"
              />
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Cài đặt hệ thống</h1>
          <p className="text-muted-foreground">
            Quản lý cài đặt và cấu hình website
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={fetchSettings} variant="outline" disabled={loading}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Làm mới
          </Button>
          <Button onClick={handleReset} variant="outline" disabled={loading}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Khôi phục mặc định
          </Button>
          <Button
            onClick={handleSave}
            disabled={loading}
            className="bg-pink-600 hover:bg-pink-700"
          >
            <Save className="h-4 w-4 mr-2" />
            {loading ? "Đang lưu..." : "Lưu cài đặt"}
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Cài đặt tổng quan</CardTitle>
          <p className="text-sm text-muted-foreground">
            Quản lý tất cả cài đặt cơ bản của website
          </p>
        </CardHeader>
        <CardContent>{renderSettings()}</CardContent>
      </Card>
    </div>
  );
}
