import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// Validation schemas
const attributePresetSchema = z.object({
  name: z.string().min(1, "Tên preset là bắt buộc"),
  slug: z.string().optional(),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
  sortOrder: z.number().default(0),
  attributes: z
    .array(
      z.object({
        attributeId: z.string().min(1),
        isRequired: z.boolean().default(false),
        defaultValueId: z.string().optional(),
        sortOrder: z.number().default(0),
      })
    )
    .optional(),
});

// GET /api/admin/attribute-presets - <PERSON><PERSON><PERSON> s<PERSON>ch presets
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search");
    const isActive = searchParams.get("isActive");
    const sortBy = searchParams.get("sortBy") || "sortOrder";
    const sortOrder = searchParams.get("sortOrder") || "asc";

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (isActive !== null && isActive !== undefined) {
      where.isActive = isActive === "true";
    }

    // Build orderBy clause
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    // Get presets with pagination
    const [presets, total] = await Promise.all([
      prisma.attributePreset.findMany({
        where,
        include: {
          attributes: {
            include: {
              attribute: {
                select: {
                  id: true,
                  name: true,
                  type: true,
                },
              },
              defaultValue: {
                select: {
                  id: true,
                  value: true,
                },
              },
            },
            orderBy: { sortOrder: "asc" },
          },
          _count: {
            select: {
              attributes: true,
              products: true,
            },
          },
        },
        orderBy,
        skip,
        take: limit,
      }),
      prisma.attributePreset.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: presets,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Get attribute presets error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách preset" },
      { status: 500 }
    );
  }
}

// POST /api/admin/attribute-presets - Tạo preset mới
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = attributePresetSchema.parse(body);

    // Generate slug if not provided
    if (!validatedData.slug) {
      validatedData.slug = validatedData.name
        .toLowerCase()
        .replace(/\s+/g, "-")
        .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
        .replace(/[èéẹẻẽêềếệểễ]/g, "e")
        .replace(/[ìíịỉĩ]/g, "i")
        .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
        .replace(/[ùúụủũưừứựửữ]/g, "u")
        .replace(/[ỳýỵỷỹ]/g, "y")
        .replace(/[đ]/g, "d")
        .replace(/[^a-z0-9-]/g, "");
    }

    // Check if slug already exists
    const existingPreset = await prisma.attributePreset.findUnique({
      where: { slug: validatedData.slug },
    });

    if (existingPreset) {
      return NextResponse.json({ error: "Slug đã tồn tại" }, { status: 400 });
    }

    // Create preset with attributes in transaction
    const preset = await prisma.$transaction(async (tx) => {
      const newPreset = await tx.attributePreset.create({
        data: {
          name: validatedData.name,
          slug: validatedData.slug!,
          description: validatedData.description,
          isActive: validatedData.isActive,
          sortOrder: validatedData.sortOrder,
        },
      });

      // Add attributes if provided
      if (validatedData.attributes && validatedData.attributes.length > 0) {
        await tx.presetAttribute.createMany({
          data: validatedData.attributes.map((attr) => ({
            presetId: newPreset.id,
            attributeId: attr.attributeId,
            isRequired: attr.isRequired,
            defaultValueId: attr.defaultValueId,
            sortOrder: attr.sortOrder,
          })),
        });
      }

      return newPreset;
    });

    // Fetch the created preset with relations
    const createdPreset = await prisma.attributePreset.findUnique({
      where: { id: preset.id },
      include: {
        attributes: {
          include: {
            attribute: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
            defaultValue: {
              select: {
                id: true,
                value: true,
              },
            },
          },
          orderBy: { sortOrder: "asc" },
        },
        _count: {
          select: {
            attributes: true,
            products: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: createdPreset,
      message: "Preset đã được tạo thành công",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Create attribute preset error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tạo preset" },
      { status: 500 }
    );
  }
}
