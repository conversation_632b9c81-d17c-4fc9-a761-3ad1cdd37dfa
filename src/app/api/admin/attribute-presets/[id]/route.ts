import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// Validation schemas
const updateAttributePresetSchema = z.object({
  name: z.string().min(1).optional(),
  slug: z.string().optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
  sortOrder: z.number().optional(),
  attributes: z
    .array(
      z.object({
        id: z.string().optional(),
        attributeId: z.string().min(1),
        isRequired: z.boolean().default(false),
        defaultValueId: z.string().optional(),
        sortOrder: z.number().default(0),
      })
    )
    .optional(),
});

// GET /api/admin/attribute-presets/[id] - Get single preset
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const preset = await prisma.attributePreset.findUnique({
      where: { id: params.id },
      include: {
        attributes: {
          include: {
            attribute: {
              include: {
                values: {
                  orderBy: { sortOrder: "asc" },
                },
              },
            },
            defaultValue: true,
          },
          orderBy: { sortOrder: "asc" },
        },
        _count: {
          select: {
            attributes: true,
            products: true,
          },
        },
      },
    });

    if (!preset) {
      return NextResponse.json(
        { success: false, error: "Không tìm thấy preset" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: preset,
    });
  } catch (error) {
    console.error("Get preset error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy thông tin preset" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/attribute-presets/[id] - Update preset
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = updateAttributePresetSchema.parse(body);

    // Check if preset exists
    const existingPreset = await prisma.attributePreset.findUnique({
      where: { id: params.id },
    });

    if (!existingPreset) {
      return NextResponse.json(
        { success: false, error: "Không tìm thấy preset" },
        { status: 404 }
      );
    }

    // Check if slug already exists (if updating slug)
    if (validatedData.slug && validatedData.slug !== existingPreset.slug) {
      const slugExists = await prisma.attributePreset.findUnique({
        where: { slug: validatedData.slug },
      });

      if (slugExists) {
        return NextResponse.json(
          { success: false, error: "Slug đã tồn tại" },
          { status: 400 }
        );
      }
    }

    // Update preset with attributes in transaction
    const preset = await prisma.$transaction(async (tx) => {
      // Update preset basic info
      const updatedPreset = await tx.attributePreset.update({
        where: { id: params.id },
        data: {
          name: validatedData.name,
          slug: validatedData.slug,
          description: validatedData.description,
          isActive: validatedData.isActive,
          sortOrder: validatedData.sortOrder,
        },
      });

      // Update attributes if provided
      if (validatedData.attributes) {
        // Delete existing preset attributes
        await tx.presetAttribute.deleteMany({
          where: { presetId: params.id },
        });

        // Create new preset attributes
        if (validatedData.attributes.length > 0) {
          await tx.presetAttribute.createMany({
            data: validatedData.attributes.map((attr) => ({
              presetId: params.id,
              attributeId: attr.attributeId,
              isRequired: attr.isRequired,
              defaultValueId: attr.defaultValueId,
              sortOrder: attr.sortOrder,
            })),
          });
        }
      }

      return updatedPreset;
    });

    // Fetch updated preset with relations
    const updatedPreset = await prisma.attributePreset.findUnique({
      where: { id: preset.id },
      include: {
        attributes: {
          include: {
            attribute: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
            defaultValue: {
              select: {
                id: true,
                value: true,
              },
            },
          },
          orderBy: { sortOrder: "asc" },
        },
        _count: {
          select: {
            attributes: true,
            products: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedPreset,
      message: "Preset đã được cập nhật thành công",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Update preset error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật preset" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/attribute-presets/[id] - Delete preset
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    // Check if preset exists
    const existingPreset = await prisma.attributePreset.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    if (!existingPreset) {
      return NextResponse.json(
        { success: false, error: "Không tìm thấy preset" },
        { status: 404 }
      );
    }

    // Check if preset is being used by products
    if (existingPreset._count.products > 0) {
      return NextResponse.json(
        {
          success: false,
          error: `Không thể xóa preset vì đang được sử dụng bởi ${existingPreset._count.products} sản phẩm`,
        },
        { status: 400 }
      );
    }

    // Delete preset (cascade will handle preset attributes)
    await prisma.attributePreset.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      success: true,
      message: "Preset đã được xóa thành công",
    });
  } catch (error) {
    console.error("Delete preset error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xóa preset" },
      { status: 500 }
    );
  }
}
