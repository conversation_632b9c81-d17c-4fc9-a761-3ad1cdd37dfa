import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { prisma } from "@/lib/prisma";

// GET /api/admin/attribute-presets/stats - Get preset statistics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    // Get statistics
    const [total, active, inactive, totalAttributes, totalProducts] =
      await Promise.all([
        prisma.attributePreset.count(),
        prisma.attributePreset.count({ where: { isActive: true } }),
        prisma.attributePreset.count({ where: { isActive: false } }),
        prisma.presetAttribute.count(),
        prisma.product.count({ where: { presetId: { not: null } } }),
      ]);

    const stats = {
      total,
      active,
      inactive,
      totalAttributes,
      totalProducts,
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error("Get preset stats error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy thống kê preset" },
      { status: 500 }
    );
  }
}
