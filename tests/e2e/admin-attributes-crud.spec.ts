import { test, expect, Page } from "@playwright/test";
import { loginAsAdmin } from "./helpers/auth.helper";

interface TestAttribute {
  name: string;
  description: string;
  type: "TEXT" | "NUMBER" | "SELECT" | "BOOLEAN" | "COLOR";
  required: boolean;
  values?: Array<{
    value: string;
    label: string;
  }>;
}

interface TestAttributePreset {
  name: string;
  description: string;
  attributes: Array<{
    attributeName: string;
    required: boolean;
    defaultValue?: string;
  }>;
}

const TEST_ATTRIBUTES: TestAttribute[] = [
  {
    name: "<PERSON><PERSON><PERSON> sắc",
    description: "<PERSON><PERSON><PERSON> sắc của sản phẩm",
    type: "SELECT",
    required: true,
    values: [
      { value: "red", label: "Đỏ" },
      { value: "blue", label: "Xanh dương" },
      { value: "green", label: "Xanh lá" },
    ],
  },
  {
    name: "<PERSON><PERSON><PERSON> thướ<PERSON>",
    description: "<PERSON><PERSON><PERSON> thước sản phẩm",
    type: "SELECT",
    required: true,
    values: [
      { value: "s", label: "S" },
      { value: "m", label: "M" },
      { value: "l", label: "L" },
      { value: "xl", label: "XL" },
    ],
  },
  {
    name: "Chất liệu",
    description: "Chất liệu của sản phẩm",
    type: "TEXT",
    required: false,
  },
  {
    name: "Trọng lượng",
    description: "Trọng lượng sản phẩm (gram)",
    type: "NUMBER",
    required: false,
  },
];

const TEST_PRESETS: TestAttributePreset[] = [
  {
    name: "Áo thun",
    description: "Preset cho sản phẩm áo thun",
    attributes: [
      { attributeName: "Màu sắc", required: true, defaultValue: "blue" },
      { attributeName: "Kích thước", required: true, defaultValue: "m" },
      { attributeName: "Chất liệu", required: false, defaultValue: "Cotton" },
    ],
  },
  {
    name: "Giày dép",
    description: "Preset cho sản phẩm giày dép",
    attributes: [
      { attributeName: "Màu sắc", required: true },
      { attributeName: "Kích thước", required: true },
    ],
  },
];

test.describe("Admin Attributes CRUD", () => {
  test.beforeEach(async ({ page }) => {
    await loginAsAdmin(page);
  });

  test.describe("Attributes Management", () => {
    test("should create a new attribute with values", async ({ page }) => {
      const attribute = TEST_ATTRIBUTES[0];

      // Navigate to attributes page
      await page.goto("/admin/attributes");
      await expect(page).toHaveURL("/admin/attributes");

      // Click create button
      await page.getByRole("button", { name: "Thêm thuộc tính" }).click();
      await expect(page).toHaveURL("/admin/attributes/create");

      // Fill basic information
      await page.getByLabel("Tên thuộc tính").fill(attribute.name);
      await page.getByLabel("Mô tả").fill(attribute.description);
      await page.getByLabel("Loại dữ liệu").selectOption(attribute.type);
      
      if (attribute.required) {
        await page.getByLabel("Bắt buộc").check();
      }

      // Add attribute values for SELECT type
      if (attribute.type === "SELECT" && attribute.values) {
        for (const value of attribute.values) {
          await page.getByRole("button", { name: "Thêm giá trị" }).click();
          
          // Fill the last added value inputs
          const valueInputs = page.locator('input[placeholder="Giá trị"]');
          const labelInputs = page.locator('input[placeholder="Nhãn hiển thị"]');
          
          const lastValueIndex = await valueInputs.count() - 1;
          await valueInputs.nth(lastValueIndex).fill(value.value);
          await labelInputs.nth(lastValueIndex).fill(value.label);
        }
      }

      // Submit form
      await page.getByRole("button", { name: "Tạo thuộc tính" }).click();

      // Verify success
      await expect(page.getByText("Thuộc tính đã được tạo thành công")).toBeVisible();
      await expect(page).toHaveURL("/admin/attributes");

      // Verify attribute appears in list
      await expect(page.getByText(attribute.name)).toBeVisible();
      await expect(page.getByText(attribute.description)).toBeVisible();
    });

    test("should edit an existing attribute", async ({ page }) => {
      // First create an attribute
      await createTestAttribute(page, TEST_ATTRIBUTES[2]);

      // Navigate to attributes list
      await page.goto("/admin/attributes");

      // Find and click edit button for the test attribute
      const attributeRow = page.locator("tr").filter({ hasText: TEST_ATTRIBUTES[2].name });
      await attributeRow.getByRole("button", { name: "Chỉnh sửa" }).click();

      // Update the attribute
      const updatedName = `${TEST_ATTRIBUTES[2].name} - Updated`;
      await page.getByLabel("Tên thuộc tính").fill(updatedName);
      await page.getByLabel("Mô tả").fill("Updated description");

      // Submit changes
      await page.getByRole("button", { name: "Cập nhật thuộc tính" }).click();

      // Verify success
      await expect(page.getByText("Thuộc tính đã được cập nhật thành công")).toBeVisible();
      await expect(page).toHaveURL("/admin/attributes");

      // Verify changes appear in list
      await expect(page.getByText(updatedName)).toBeVisible();
    });

    test("should delete an attribute", async ({ page }) => {
      // First create an attribute
      await createTestAttribute(page, TEST_ATTRIBUTES[3]);

      // Navigate to attributes list
      await page.goto("/admin/attributes");

      // Find and click delete button
      const attributeRow = page.locator("tr").filter({ hasText: TEST_ATTRIBUTES[3].name });
      await attributeRow.getByRole("button", { name: "Xóa" }).click();

      // Confirm deletion
      await page.getByRole("button", { name: "Xóa" }).click();

      // Verify success
      await expect(page.getByText("Thuộc tính đã được xóa thành công")).toBeVisible();

      // Verify attribute is removed from list
      await expect(page.getByText(TEST_ATTRIBUTES[3].name)).not.toBeVisible();
    });

    test("should validate required fields", async ({ page }) => {
      await page.goto("/admin/attributes/create");

      // Try to submit without filling required fields
      await page.getByRole("button", { name: "Tạo thuộc tính" }).click();

      // Verify validation errors
      await expect(page.getByText("Tên thuộc tính là bắt buộc")).toBeVisible();
      await expect(page.getByText("Loại dữ liệu là bắt buộc")).toBeVisible();
    });
  });

  test.describe("Attribute Presets Management", () => {
    test("should create a new attribute preset", async ({ page }) => {
      // First ensure we have attributes to use
      for (const attr of TEST_ATTRIBUTES.slice(0, 2)) {
        await createTestAttribute(page, attr);
      }

      const preset = TEST_PRESETS[0];

      // Navigate to presets page
      await page.goto("/admin/attribute-presets");
      await expect(page).toHaveURL("/admin/attribute-presets");

      // Click create button
      await page.getByRole("button", { name: "Thêm preset" }).click();
      await expect(page).toHaveURL("/admin/attribute-presets/create");

      // Fill basic information
      await page.getByLabel("Tên preset").fill(preset.name);
      await page.getByLabel("Mô tả").fill(preset.description);

      // Add attributes to preset
      for (const attr of preset.attributes) {
        await page.getByRole("button", { name: "Thêm thuộc tính" }).click();
        
        // Select attribute from dropdown
        const attributeSelects = page.locator('select[name*="attributeId"]');
        const lastSelectIndex = await attributeSelects.count() - 1;
        await attributeSelects.nth(lastSelectIndex).selectOption({ label: attr.attributeName });

        // Set required status
        if (attr.required) {
          const requiredCheckboxes = page.locator('input[type="checkbox"][name*="required"]');
          await requiredCheckboxes.nth(lastSelectIndex).check();
        }

        // Set default value if provided
        if (attr.defaultValue) {
          const defaultValueInputs = page.locator('input[name*="defaultValue"]');
          await defaultValueInputs.nth(lastSelectIndex).fill(attr.defaultValue);
        }
      }

      // Submit form
      await page.getByRole("button", { name: "Tạo preset" }).click();

      // Verify success
      await expect(page.getByText("Preset đã được tạo thành công")).toBeVisible();
      await expect(page).toHaveURL("/admin/attribute-presets");

      // Verify preset appears in list
      await expect(page.getByText(preset.name)).toBeVisible();
    });

    test("should use preset when creating product", async ({ page }) => {
      // First create a preset
      await createTestPreset(page, TEST_PRESETS[0]);

      // Navigate to product creation
      await page.goto("/admin/products/create");

      // Select the preset
      await page.getByLabel("Chọn preset thuộc tính").selectOption({ label: TEST_PRESETS[0].name });

      // Verify preset attributes are loaded
      await expect(page.getByText("Màu sắc")).toBeVisible();
      await expect(page.getByText("Kích thước")).toBeVisible();
      await expect(page.getByText("Chất liệu")).toBeVisible();

      // Verify default values are set
      const colorSelect = page.locator('select').filter({ hasText: "Màu sắc" });
      await expect(colorSelect).toHaveValue("blue");
    });
  });
});

// Helper functions
async function createTestAttribute(page: Page, attribute: TestAttribute) {
  await page.goto("/admin/attributes/create");
  
  await page.getByLabel("Tên thuộc tính").fill(attribute.name);
  await page.getByLabel("Mô tả").fill(attribute.description);
  await page.getByLabel("Loại dữ liệu").selectOption(attribute.type);
  
  if (attribute.required) {
    await page.getByLabel("Bắt buộc").check();
  }

  if (attribute.type === "SELECT" && attribute.values) {
    for (const value of attribute.values) {
      await page.getByRole("button", { name: "Thêm giá trị" }).click();
      
      const valueInputs = page.locator('input[placeholder="Giá trị"]');
      const labelInputs = page.locator('input[placeholder="Nhãn hiển thị"]');
      
      const lastValueIndex = await valueInputs.count() - 1;
      await valueInputs.nth(lastValueIndex).fill(value.value);
      await labelInputs.nth(lastValueIndex).fill(value.label);
    }
  }

  await page.getByRole("button", { name: "Tạo thuộc tính" }).click();
  await expect(page.getByText("Thuộc tính đã được tạo thành công")).toBeVisible();
}

async function createTestPreset(page: Page, preset: TestAttributePreset) {
  // Ensure attributes exist first
  for (const attr of preset.attributes) {
    const testAttr = TEST_ATTRIBUTES.find(a => a.name === attr.attributeName);
    if (testAttr) {
      await createTestAttribute(page, testAttr);
    }
  }

  await page.goto("/admin/attribute-presets/create");
  
  await page.getByLabel("Tên preset").fill(preset.name);
  await page.getByLabel("Mô tả").fill(preset.description);

  for (const attr of preset.attributes) {
    await page.getByRole("button", { name: "Thêm thuộc tính" }).click();
    
    const attributeSelects = page.locator('select[name*="attributeId"]');
    const lastSelectIndex = await attributeSelects.count() - 1;
    await attributeSelects.nth(lastSelectIndex).selectOption({ label: attr.attributeName });

    if (attr.required) {
      const requiredCheckboxes = page.locator('input[type="checkbox"][name*="required"]');
      await requiredCheckboxes.nth(lastSelectIndex).check();
    }

    if (attr.defaultValue) {
      const defaultValueInputs = page.locator('input[name*="defaultValue"]');
      await defaultValueInputs.nth(lastSelectIndex).fill(attr.defaultValue);
    }
  }

  await page.getByRole("button", { name: "Tạo preset" }).click();
  await expect(page.getByText("Preset đã được tạo thành công")).toBeVisible();
}
