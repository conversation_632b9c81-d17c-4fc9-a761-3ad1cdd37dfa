/**
 * Test utilities for unit and integration tests
 */

import { User, AdminUser, Product, Category, Order } from "@prisma/client";

// Mock user data factory
export function createMockUser(overrides: Partial<User> = {}): User {
  return {
    id: "user-123",
    email: "<EMAIL>",
    name: "Test User",
    password: "hashedpassword",
    avatar: null,
    phone: null,
    dateOfBirth: null,
    gender: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  };
}

// Mock admin user data factory
export function createMockAdminUser(
  overrides: Partial<AdminUser> = {}
): AdminUser {
  return {
    id: "admin-123",
    email: "<EMAIL>",
    name: "Test Admin",
    password: "hashedpassword",
    role: "MODERATOR",
    department: "IT",
    permissions: ["READ", "WRITE"],
    isActive: true,
    lastLoginAt: null,
    createdById: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  };
}

// Mock product data factory
export function createMockProduct(overrides: Partial<Product> = {}): Product {
  return {
    id: "product-123",
    name: "Test Product",
    slug: "test-product",
    description: "Test product description",
    shortDescription: "Short description",
    price: 100,
    salePrice: null,
    sku: "TEST-SKU-123",
    stock: 10,
    categoryId: "category-123",
    brandId: null,
    images: ["image1.jpg", "image2.jpg"],
    featured: false,
    status: "ACTIVE",
    tags: ["test", "product"],
    weight: null,
    dimensions: null,
    seoTitle: null,
    seoDescription: null,
    seoKeywords: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  };
}

// Mock category data factory
export function createMockCategory(
  overrides: Partial<Category> = {}
): Category {
  return {
    id: "category-123",
    name: "Test Category",
    slug: "test-category",
    description: "Test category description",
    image: "category.jpg",
    parentId: null,
    sortOrder: 0,
    isActive: true,
    seoTitle: null,
    seoDescription: null,
    seoKeywords: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  };
}

// Mock order data factory
export function createMockOrder(overrides: Partial<Order> = {}): Order {
  return {
    id: "order-123",
    orderNumber: "ORD-123456",
    userId: "user-123",
    status: "PENDING",
    paymentStatus: "PENDING",
    paymentMethod: "CREDIT_CARD",
    shippingMethod: "STANDARD",
    subtotal: 100,
    shippingCost: 10,
    tax: 5,
    discount: 0,
    total: 115,
    currency: "VND",
    notes: null,
    shippingAddress: {
      fullName: "Test User",
      phone: "0123456789",
      address: "123 Test St",
      city: "Test City",
      state: "Test State",
      postalCode: "12345",
      country: "Vietnam",
    },
    billingAddress: {
      fullName: "Test User",
      phone: "0123456789",
      address: "123 Test St",
      city: "Test City",
      state: "Test State",
      postalCode: "12345",
      country: "Vietnam",
    },
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
  };
}

// Mock NextRequest for testing
export function createMockNextRequest(
  url: string = "http://localhost:3000",
  options: RequestInit = {}
) {
  // Create a mock request object that doesn't use NextRequest constructor
  // to avoid the "Cannot set property url" error
  const mockRequest = {
    url,
    method: options.method || "GET",
    headers: new Headers(options.headers),
    body: options.body,
    nextUrl: {
      pathname: new URL(url).pathname,
      searchParams: new URL(url).searchParams,
    },
    json: async () => {
      if (options.body && typeof options.body === "string") {
        return JSON.parse(options.body);
      }
      return {};
    },
    text: async () => {
      return options.body?.toString() || "";
    },
    formData: async () => {
      return new FormData();
    },
  };

  return mockRequest as any; // Type assertion to avoid TypeScript issues
}

// Mock NextResponse for testing
export function createMockNextResponse(
  data: any = {},
  status: number = 200,
  headers: Record<string, string> = {}
) {
  return {
    status,
    headers: new Headers(headers),
    json: async () => data,
    text: async () => JSON.stringify(data),
    ok: status >= 200 && status < 300,
  };
}

// Test database helpers
export function resetMockDatabase() {
  // Reset all mock implementations
  jest.clearAllMocks();
}

// Mock session data
export function createMockSession(overrides: any = {}) {
  return {
    user: {
      id: "user-123",
      email: "<EMAIL>",
      name: "Test User",
      role: "USER",
    },
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    ...overrides,
  };
}

// Mock admin session data
export function createMockAdminSession(overrides: any = {}) {
  return {
    user: {
      id: "admin-123",
      email: "<EMAIL>",
      name: "Test Admin",
      role: "ADMIN",
    },
    expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    ...overrides,
  };
}

// Utility to wait for async operations in tests
export function waitFor(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// Mock console methods for testing
export function mockConsole() {
  const originalConsole = { ...console };
  const mockMethods = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
  };

  Object.assign(console, mockMethods);

  return {
    restore: () => Object.assign(console, originalConsole),
    mocks: mockMethods,
  };
}

// Generate test data arrays
export function generateMockUsers(count: number): User[] {
  return Array.from({ length: count }, (_, index) =>
    createMockUser({
      id: `user-${index + 1}`,
      email: `user${index + 1}@example.com`,
      name: `User ${index + 1}`,
    })
  );
}

export function generateMockProducts(count: number): Product[] {
  return Array.from({ length: count }, (_, index) =>
    createMockProduct({
      id: `product-${index + 1}`,
      name: `Product ${index + 1}`,
      slug: `product-${index + 1}`,
      sku: `SKU-${index + 1}`,
      price: (index + 1) * 10,
    })
  );
}
